import './App.css'
import reactLogo from '@/assets/react.svg'
import {Button} from "@/components/ui/button"
import {
    Accordion,
    AccordionContent,
    AccordionItem,
    AccordionTrigger,
} from "@/components/ui/accordion"

function App() {
    return (
        <>
            <Accordion type="single" collapsible className="w-full">
                <AccordionItem value="item-1">
                    <AccordionTrigger>Is it accessible?</AccordionTrigger>
                    <AccordionContent>
                        Yes. It adheres to the WAI-ARIA design pattern.
                    </AccordionContent>
                </AccordionItem>
                <AccordionItem value="item-2">
                    <AccordionTrigger>Is it styled?</AccordionTrigger>
                    <AccordionContent>
                        Yes. It comes with default styles that matches the other
                        components&apos; aesthetic.
                    </AccordionContent>
                </AccordionItem>
                <AccordionItem value="item-3">
                    <AccordionTrigger>Is it animated?</AccordionTrigger>
                    <AccordionContent>
                        Yes. It's animated by default, but you can disable it if you prefer.
                    </AccordionContent>
                </AccordionItem>
            </Accordion>


            <div className="bg-background text-foreground">
                <Button>Secondary</Button>

                <div
                    className="mx-auto flex max-w-sm items-center gap-x-4 rounded-xl bg-white p-6 shadow-lg outline outline-black/5 dark:bg-slate-800 dark:shadow-none dark:-outline-offset-1 dark:outline-white/10">
                    <img className="size-12 shrink-0" src={reactLogo} alt="ChitChat Logo"/>
                    <div>
                        <div className="text-xl font-medium text-black dark:text-white">ChitChat</div>
                        <p className="text-gray-500 dark:text-gray-400">You have a new message!</p>
                    </div>
                </div>
                <h1 className="text-4xl font-bold underline">
                    Hello world!
                </h1>
                <div>
                    react app
                </div>
            </div>

        </>
    )

}

export default App
